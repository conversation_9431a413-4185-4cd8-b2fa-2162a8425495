<template>
  <div class="report-title">
    <h2>员工信息统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="员工姓名">
              <el-input
                v-model="queryForm.employeeName"
                placeholder="请输入员工姓名"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date_range" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="chemployeename" label="员工姓名" />
        <el-table-column prop="chemployee_hrcode" label="员工编号" />
        <el-table-column prop="chpost_name" label="岗位" />
        <el-table-column prop="hire_date" label="入职日期" />
        // 新增离职日期列
        <el-table-column prop="termination_date" label="离职日期" />
        <el-table-column prop="mkt_prsn_ior" label="员工投产比" align="right" />
        // 新增年龄列
        <el-table-column prop="age" label="年龄" align="right" />
        <el-table-column prop="company_seniority" label="司龄" align="right" />
        <el-table-column prop="gender" label="性别" />
        <el-table-column prop="highest_education" label="最高学历" />
        <el-table-column prop="passed_exams" label="通过考试情况" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const route = useRoute()

// 查询表单
const queryForm = reactive({
  startDate: '',
  branchCode: '',
  employeeName: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 表格数据
const tableData = ref([])

// 初始化数据
onMounted(async () => {
  // 获取上个月的日期
  const lastMonth = new Date()
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  const year = lastMonth.getFullYear()
  const month = String(lastMonth.getMonth() + 1).padStart(2, '0')
  queryForm.startDate = `${year}-${month}`

  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  loading.value = true

  // 构建函数参数
  const functionParams = {
    oacode: oacode.value,
    srcsys: srcsys.value,
    p_data_date: queryForm.startDate,
    p_brh_cd: queryForm.branchCode,
    p_chemployeename: queryForm.employeeName || null,
    p_page_num: currentPage.value,
    p_page_size: pageSize.value
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_employee_info_statistics_s', functionParams, config)
    .then(response => {
      const rawData = response.data || []

      // 由于函数已经进行了汇总，直接使用返回的数据
      // 但需要进行分页处理
      const startIndex = (currentPage.value - 1) * pageSize.value
      const endIndex = startIndex + pageSize.value
      const paginatedData = rawData.slice(startIndex, endIndex)

      tableData.value = paginatedData
      totalCount.value = rawData.length
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: oacode.value,
      srcsys: srcsys.value,
      p_data_date: queryForm.startDate,
      p_brh_cd: queryForm.branchCode,
      p_chemployeename: queryForm.employeeName || null,
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_employee_info_statistics_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '员工姓名', '员工编号', '岗位', '入职日期', '离职日期', '员工投产比', '年龄', '司龄', '性别', '最高学历', '通过考试情况'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date_range || '',
        item.brh_nam || '',
        item.branch_name || '',
        item.chemployeename || '',
        item.chemployee_hrcode || '',
        item.chpost_name || '',
        item.hire_date || '',
        // 新增离职日期字段
        item.termination_date || '',
        item.mkt_prsn_ior || '',
        // 新增年龄字段
        item.age || '',
        item.company_seniority || '',
        item.gender || '',
        item.highest_education || '',
        item.passed_exams || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 员工姓名
      { wch: 15 }, // 员工编号
      { wch: 15 }, // 岗位
      { wch: 15 }, // 入职日期
      // 离职日期列宽
      { wch: 15 }, // 离职日期
      { wch: 15 }, // 员工投产比
      // 年龄列宽
      { wch: 15 }, // 年龄
      { wch: 15 }, // 司龄
      { wch: 15 }, // 性别
      { wch: 15 }, // 最高学历
      { wch: 15 }, // 通过考试情况
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '员工信息统计')

    // 生成文件名
    const now = new Date()
    const fileName = `员工信息统计_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}
</script>

<style lang="scss" scoped>
// test
</style>