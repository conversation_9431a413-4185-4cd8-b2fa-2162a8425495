<template>
  <div class="report-title">
    <h2>客户数据统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计开始日期" required>
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="统计截止日期" required>
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="投资者代码">
              <el-input
                v-model="queryForm.investorCode"
                placeholder="请输入投资者代码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="投资者名称">
              <el-input
                v-model="queryForm.investorName"
                placeholder="请输入投资者名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户统计方式" required>
              <el-select
                v-model="queryForm.custStatMode"
                placeholder="请选择客户统计方式"
              >
                <el-option label="客户统计" value="customer" />
                <el-option label="营业部统计" value="branch" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="机构归属" required>
              <el-select
                v-model="queryForm.orgBelong"
                placeholder="请选择机构归属"
              >
                <el-option label="历史归属" value="historical" />
                <el-option label="最新归属" value="latest" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery" :loading="loading">查询</el-button>
              <el-button type="warning" @click="handleExport" :loading="exportLoading">
                {{ exportLoading ? '导出中...' : '导出' }}
              </el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date_range" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" sortable="custom"/>
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="ivst_cd" label="投资者代码" sortable="custom"/>
        <el-table-column prop="ivst_name" label="投资者名称" sortable="custom"/>
        <el-table-column prop="ivst_type" label="投资者类型" />
        <el-table-column prop="emp_cd" label="业务人员代码" />
        <el-table-column prop="emp_name" label="业务人员名称" />
        <el-table-column prop="openacct_dt" label="开户日期" />
        <el-table-column prop="avg_equi" label="日均权益" align="right" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.avg_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="end_equi" label="期末权益" align="right" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.end_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="deposit" label="入金" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.deposit) }}
          </template>
        </el-table-column>
        <el-table-column prop="withdraw" label="出金" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.withdraw) }}
          </template>
        </el-table-column>
        <el-table-column prop="fee" label="手续费" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.fee) }}
          </template>
        </el-table-column>
        <el-table-column prop="fee_paid_up" label="上交手续费" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.fee_paid_up) }}
          </template>
        </el-table-column>
        <el-table-column prop="fee_retained" label="留存手续费" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.fee_retained) }}
          </template>
        </el-table-column>
        <el-table-column prop="trade_lots" label="成交手数" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.trade_lots) }}
          </template>
        </el-table-column>
        <el-table-column prop="trade_amt" label="成交金额" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.trade_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_pnl" label="总盈亏" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.total_pnl) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认日期
const getDefaultDates = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() // 0-11

  // 当年一月
  const startDate = `${currentYear}-01`

  // 当前系统时间上一个月份
  let endYear = currentYear
  let endMonth = currentMonth // 当前月份的上一个月

  if (endMonth === 0) { // 如果当前是1月，上一个月是去年12月
    endYear = currentYear - 1
    endMonth = 12
  }

  const endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`

  return { startDate, endDate }
}

const defaultDates = getDefaultDates()

// 查询表单
const queryForm = reactive({
  startDate: defaultDates.startDate,
  endDate: defaultDates.endDate,
  branchCode: '',
  investorCode: '',
  investorName: '',
  custStatMode: 'customer',
  orgBelong: 'historical'
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)
const exportLoading = ref(false)

// 排序参数
const sortField = ref('ivst_cd')
const sortDir = ref('ASC')

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  // 验证日期范围
  if (queryForm.startDate > queryForm.endDate) {
    ElMessage.warning('统计开始日期不能大于统计截止日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数 - 使用函数入参分页模式
  const functionParams = {
    oacode: urlParams.oacode,
    srcsys: urlParams.srcsys,
    p_start_date: queryForm.startDate,
    p_end_date: queryForm.endDate,
    p_brh_cd: queryForm.branchCode || null,
    p_ivst_cd: queryForm.investorCode || null,
    p_ivst_name: queryForm.investorName || null,
    p_cust_stat_mode: queryForm.custStatMode || null,
    p_org_belong: queryForm.orgBelong || null,
    // 新增分页参数
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    sortField: sortField.value,
    sortDir: sortDir.value
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_cust_data_stat_s', functionParams, config)
  .then(response => {
    const rawData = response.data || []

    if (rawData.length > 0) {
      // 函数返回的数据已经分页，直接使用
      tableData.value = rawData

      // 从第一条记录获取总记录数
      const firstRecord = rawData[0]
      totalCount.value = firstRecord.total_records || 0

      // 计算总记录数（总页数 * 页面大小，最后一页可能不满）

    } else {
      tableData.value = []
      totalCount.value = 0
    }
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法 - 使用并发请求
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  // 验证日期范围
  if (queryForm.startDate > queryForm.endDate) {
    ElMessage.warning('统计开始日期不能大于统计截止日期')
    return
  }

  // 防止重复点击
  if (exportLoading.value) {
    return
  }

  exportLoading.value = true

  console.log('导出开始时间:', new Date().toLocaleString())
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 先获取第一页数据以获取总记录数
    const firstPageParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      p_start_date: queryForm.startDate,
      p_end_date: queryForm.endDate,
      p_brh_cd: queryForm.branchCode || null,
      p_ivst_cd: queryForm.investorCode || null,
      p_ivst_name: queryForm.investorName || null,
      p_cust_stat_mode: queryForm.custStatMode || null,
      p_org_belong: queryForm.orgBelong || null,
      pageSize: 50000,
      pageNum: 1,
      sortField: sortField.value,
      sortDir: sortDir.value
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    const firstResponse = await http.callFunction('p_cust_data_stat_s', firstPageParams, config)
    const firstPageData = firstResponse.data || []

    if (firstPageData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    const totalRecords = firstPageData[0].total_records || 0
    let allData = [...firstPageData]

    // 如果有多页数据，并发请求剩余页面
    const totalPagesCount = Math.ceil(totalRecords / 50000)
    if (totalPagesCount > 1) {
      const promises = []

      // 创建并发请求数组
      for (let page = 2; page <= totalPagesCount; page++) {
        const pageParams = {
          ...firstPageParams,
          pageNum: page
        }

        promises.push(
          http.callFunction('p_cust_data_stat_s', pageParams, config)
        )
      }

      // 并发执行所有请求
      const responses = await Promise.all(promises)

      // 合并所有数据
      responses.forEach(response => {
        const pageData = response.data || []
        allData = allData.concat(pageData)
      })
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '投资者代码', '投资者名称', '投资者类型',
        '业务人员代码', '业务人员名称', '开户日期', '日均权益', '期末权益', '入金',
        '出金', '手续费', '上交手续费', '留存手续费', '成交手数', '成交金额', '总盈亏'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date_range ?? '',
        item.brh_cd ?? '',
        item.brh_nam ?? '',
        item.ivst_cd ?? '',
        item.ivst_name ?? '',
        item.ivst_type ?? '',
        item.emp_cd ?? '',
        item.emp_name ?? '',
        item.openacct_dt ?? '',
        formatNumber(item.avg_equi),
        formatNumber(item.end_equi),
        formatNumber(item.deposit),
        formatNumber(item.withdraw),
        formatNumber(item.fee),
        formatNumber(item.fee_paid_up),
        formatNumber(item.fee_retained),
        formatNumber(item.trade_lots),
        formatNumber(item.trade_amt),
        formatNumber(item.total_pnl)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 投资者代码
      { wch: 20 }, // 投资者名称
      { wch: 12 }, // 投资者类型
      { wch: 15 }, // 业务人员代码
      { wch: 15 }, // 业务人员名称
      { wch: 12 }, // 开户日期
      { wch: 15 }, // 日均权益
      { wch: 15 }, // 期末权益
      { wch: 15 }, // 入金
      { wch: 15 }, // 出金
      { wch: 15 }, // 手续费
      { wch: 15 }, // 上交手续费
      { wch: 15 }, // 留存手续费
      { wch: 15 }, // 成交手数
      { wch: 15 }, // 成交金额
      { wch: 15 }  // 总盈亏
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '客户数据统计表')

    // 生成文件名
    const now = new Date()
    const fileName = `客户数据统计表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  } finally {
    exportLoading.value = false
    console.log('导出结束时间:', new Date().toLocaleString())
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 处理排序变化
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    sortField.value = prop
    sortDir.value = order === 'ascending' ? 'ASC' : 'DESC'
  } else {
    sortField.value = 'ivst_cd'
    sortDir.value = 'ASC'
  }
  currentPage.value = 1 // 排序后回到第一页
  handleQuery()
}


</script>

<style lang="scss" scoped>
/* temp  */
</style>
