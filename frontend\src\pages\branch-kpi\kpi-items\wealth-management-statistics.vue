<template>
  <div class="report-title">
    <h2>财富管理业务统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计开始日期" required>
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="统计截止日期" required>
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品类型">
              <el-select
                v-model="queryForm.productType"
                filterable
                clearable
                placeholder="请选择产品类型"
              >
                <el-option
                  v-for="item in productTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="产品代码">
              <el-input
                v-model="queryForm.productCode"
                placeholder="请输入产品代码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品名称">
              <el-input
                v-model="queryForm.productName"
                placeholder="请输入产品名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="eval_brh_cd" label="考核归属分支机构代码" />
        <el-table-column prop="eval_brh_nam" label="考核归属分支机构名称" />
        <el-table-column prop="prod_cd" label="产品代码" />
        <el-table-column prop="prod_name" label="产品名称" />
        <el-table-column prop="prod_type" label="产品类型" />
        <el-table-column prop="issue_date" label="发行日期" />
        <el-table-column prop="redeem_date" label="赎回日期" />
        <el-table-column prop="emp_cd" label="业务人员代码" />
        <el-table-column prop="emp_name" label="业务人员名称" />
        <el-table-column prop="eval_emp_cd" label="考核业务人员代码" />
        <el-table-column prop="eval_emp_name" label="考核业务人员名称" />
        <el-table-column prop="cust_name" label="客户名称" />
        <el-table-column prop="sales_amt" label="销售额" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.sales_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="income_amt" label="销售收入" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.income_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="equi_ratio" label="权益折算系数" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.equi_ratio) }}
          </template>
        </el-table-column>
        <el-table-column prop="innov_equi" label="创新权益" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.innov_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="valid_cust_conv" label="有效户折算户数" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.valid_cust_conv) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认日期
const getDefaultDates = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() // 0-11

  // 当年一月
  const startDate = `${currentYear}-01`

  // 当前系统时间上一个月份
  let endYear = currentYear
  let endMonth = currentMonth // 当前月份的上一个月

  if (endMonth === 0) { // 如果当前是1月，上一个月是去年12月
    endYear = currentYear - 1
    endMonth = 12
  }

  const endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`

  return { startDate, endDate }
}

const defaultDates = getDefaultDates()

// 查询表单
const queryForm = reactive({
  startDate: defaultDates.startDate,
  endDate: defaultDates.endDate,
  branchCode: '',
  productType: '',
  productCode: '',
  productName: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 产品类型选项
const productTypeOptions = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

// 获取产品类型选项
const loadProductTypeOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0006'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/v_dictionary_cfg', {}, config)
    const data = response.data || []

    productTypeOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取产品类型选项失败:', error)
    productTypeOptions.value = []
    ElMessage.warning('获取产品类型选项失败')
  }
}

// 初始化数据
onMounted(async () => {
  await loadProductTypeOptions()
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  // 验证日期范围
  if (queryForm.startDate > queryForm.endDate) {
    ElMessage.warning('统计开始日期不能大于统计截止日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数
  const functionParams = {
    oacode: urlParams.oacode,
    srcsys: urlParams.srcsys,
    p_start_date: queryForm.startDate,
    p_end_date: queryForm.endDate,
    p_brh_cd: queryForm.branchCode || null,
    p_prod_type: queryForm.productType || null,
    p_prod_cd: queryForm.productCode || null,
    p_prod_name: queryForm.productName || null
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_wealth_mgt_stat_s', functionParams, config)
  .then(response => {
    const rawData = response.data || []

    // 由于函数已经进行了汇总，直接使用返回的数据
    // 但需要进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  // 验证日期范围
  if (queryForm.startDate > queryForm.endDate) {
    ElMessage.warning('统计开始日期不能大于统计截止日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      p_start_date: queryForm.startDate,
      p_end_date: queryForm.endDate,
      p_brh_cd: queryForm.branchCode || null,
      p_prod_type: queryForm.productType || null,
      p_prod_cd: queryForm.productCode || null,
      p_prod_name: queryForm.productName || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_wealth_mgt_stat_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '考核归属分支机构代码', '考核归属分支机构名称',
        '产品代码', '产品名称', '产品类型', '发行日期', '赎回日期', '业务人员代码',
        '业务人员名称', '考核业务人员代码', '考核业务人员名称', '客户名称', '销售额',
        '销售收入', '权益折算系数', '创新权益', '有效户折算户数'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date ?? '',
        item.brh_cd ?? '',
        item.brh_nam ?? '',
        item.eval_brh_cd ?? '',
        item.eval_brh_nam ?? '',
        item.prod_cd ?? '',
        item.prod_name ?? '',
        item.prod_type ?? '',
        item.issue_date ?? '',
        item.redeem_date ?? '',
        item.emp_cd ?? '',
        item.emp_name ?? '',
        item.eval_emp_cd ?? '',
        item.eval_emp_name ?? '',
        item.cust_name ?? '',
        formatNumber(item.sales_amt),
        formatNumber(item.income_amt),
        formatNumber(item.equi_ratio),
        formatNumber(item.innov_equi),
        formatNumber(item.valid_cust_conv)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 20 }, // 考核归属分支机构代码
      { wch: 25 }, // 考核归属分支机构名称
      { wch: 15 }, // 产品代码
      { wch: 25 }, // 产品名称
      { wch: 15 }, // 产品类型
      { wch: 12 }, // 发行日期
      { wch: 12 }, // 赎回日期
      { wch: 15 }, // 业务人员代码
      { wch: 15 }, // 业务人员名称
      { wch: 18 }, // 考核业务人员代码
      { wch: 18 }, // 考核业务人员名称
      { wch: 20 }, // 客户名称
      { wch: 15 }, // 销售额
      { wch: 15 }, // 销售收入
      { wch: 15 }, // 权益折算系数
      { wch: 15 }, // 创新权益
      { wch: 18 }  // 有效户折算户数
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '财富管理业务统计表')

    // 生成文件名
    const now = new Date()
    const fileName = `财富管理业务统计表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}
</script>

<style lang="scss" scoped>
// test
</style>