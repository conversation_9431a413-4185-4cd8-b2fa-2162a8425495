<template>
  <div class="report-title">
    <h2>风险管理业务统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计开始日期" required>
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="统计截止日期" required>
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务类型">
              <el-select
                v-model="queryForm.bizType"
                filterable
                clearable
                placeholder="请选择业务类型"
              >
                <el-option
                  v-for="item in bizTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" sortable/>
        <el-table-column prop="brh_nam" label="分支机构名称" />
        <el-table-column prop="emp_cd" label="业务人员代码" />
        <el-table-column prop="emp_name" label="业务人员名称" />
        <el-table-column prop="proj_name" label="项目名称" />
        <el-table-column prop="biz_type" label="业务类型" />
        <el-table-column prop="proj_type" label="项目类型" />
        <el-table-column prop="proj_status" label="项目状态" />
        <el-table-column prop="over_alloc_biz_type" label="超额分配业务类型" />
        <el-table-column prop="over_alloc_income" label="超额分配业务收入" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.over_alloc_income) }}
          </template>
        </el-table-column>
        <el-table-column prop="premium" label="权利金" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.premium) }}
          </template>
        </el-table-column>
        <el-table-column prop="support_amt" label="支持金额" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.support_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="premium_tim_value" label="权利金时间价值" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.premium_tim_value) }}
          </template>
        </el-table-column>
        <el-table-column prop="nominal_principal" label="名义本金" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.nominal_principal) }}
          </template>
        </el-table-column>
        <el-table-column prop="incentive_policy" label="激励政策" />
        <el-table-column prop="proj_alloc_net_income" label="项目分配净收入" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.proj_alloc_net_income) }}
          </template>
        </el-table-column>
        <el-table-column prop="capital_sales_income" label="资金类销售收入" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.capital_sales_income) }}
          </template>
        </el-table-column>
        <el-table-column prop="equi_ratio" label="权益折算系数" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.equi_ratio) }}
          </template>
        </el-table-column>
        <el-table-column prop="innov_equi" label="创新权益" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.innov_equi) }}
          </template>
        </el-table-column>
        <el-table-column prop="attrib_year" label="归属年份" />
        <el-table-column prop="payment_date" label="兑现日期" />
        <el-table-column prop="valid_cust_conv" label="有效户折算户数" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.valid_cust_conv) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认日期
const getDefaultDates = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() // 0-11

  // 当年一月
  const startDate = `${currentYear}-01`

  // 当前系统时间上一个月份
  let endYear = currentYear
  let endMonth = currentMonth // 当前月份的上一个月

  if (endMonth === 0) { // 如果当前是1月，上一个月是去年12月
    endYear = currentYear - 1
    endMonth = 12
  }

  const endDate = `${endYear}-${String(endMonth).padStart(2, '0')}`

  return { startDate, endDate }
}

const defaultDates = getDefaultDates()

// 查询表单
const queryForm = reactive({
  startDate: defaultDates.startDate,
  endDate: defaultDates.endDate,
  branchCode: '',
  bizType: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 业务类型选项
const bizTypeOptions = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

// 获取业务类型选项
const loadBizTypeOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0002'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/v_dictionary_cfg', {}, config)
    const data = response.data || []

    bizTypeOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取业务类型选项失败:', error)
    bizTypeOptions.value = []
    ElMessage.warning('获取业务类型选项失败')
  }
}

// 初始化数据
onMounted(async () => {
  await loadBizTypeOptions()
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  // 验证日期范围
  if (queryForm.startDate > queryForm.endDate) {
    ElMessage.warning('统计开始日期不能大于统计截止日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数
  const functionParams = {
    oacode: urlParams.oacode,
    srcsys: urlParams.srcsys,
    p_start_date: queryForm.startDate,
    p_end_date: queryForm.endDate,
    p_brh_cd: queryForm.branchCode || null,
    p_biz_type: queryForm.bizType || null
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_risk_mgt_stat_s', functionParams, config)
  .then(response => {
    const rawData = response.data || []

    // 由于函数已经进行了汇总，直接使用返回的数据
    // 但需要进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.startDate) {
    ElMessage.warning('请选择统计开始日期')
    return
  }

  if (!queryForm.endDate) {
    ElMessage.warning('请选择统计截止日期')
    return
  }

  // 验证日期范围
  if (queryForm.startDate > queryForm.endDate) {
    ElMessage.warning('统计开始日期不能大于统计截止日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      p_start_date: queryForm.startDate,
      p_end_date: queryForm.endDate,
      p_brh_cd: queryForm.branchCode || null,
      p_biz_type: queryForm.bizType || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_risk_mgt_stat_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '业务人员代码', '业务人员名称', '项目名称',
        '业务类型', '项目类型', '项目状态', '超额分配业务类型', '超额分配业务收入', '权利金',
        '支持金额', '权利金时间价值', '名义本金', '激励政策', '项目分配净收入', '资金类销售收入',
        '权益折算系数', '创新权益', '归属年份', '兑现日期', '有效户折算户数'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date ?? '',
        item.brh_cd ?? '',
        item.brh_nam ?? '',
        item.emp_cd ?? '',
        item.emp_name ?? '',
        item.proj_name ?? '',
        item.biz_type ?? '',
        item.proj_type ?? '',
        item.proj_status ?? '',
        item.over_alloc_biz_type ?? '',
        formatNumber(item.over_alloc_income),
        formatNumber(item.premium),
        formatNumber(item.support_amt),
        formatNumber(item.premium_tim_value),
        formatNumber(item.nominal_principal),
        item.incentive_policy ?? '',
        formatNumber(item.proj_alloc_net_income),
        formatNumber(item.capital_sales_income),
        formatNumber(item.equi_ratio),
        formatNumber(item.innov_equi),
        item.attrib_year ?? '',
        item.payment_date ?? '',
        formatNumber(item.valid_cust_conv)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 业务人员代码
      { wch: 15 }, // 业务人员名称
      { wch: 25 }, // 项目名称
      { wch: 15 }, // 业务类型
      { wch: 15 }, // 项目类型
      { wch: 12 }, // 项目状态
      { wch: 18 }, // 超额分配业务类型
      { wch: 18 }, // 超额分配业务收入
      { wch: 12 }, // 权利金
      { wch: 15 }, // 支持金额
      { wch: 18 }, // 权利金时间价值
      { wch: 15 }, // 名义本金
      { wch: 15 }, // 激励政策
      { wch: 18 }, // 项目分配净收入
      { wch: 18 }, // 资金类销售收入
      { wch: 15 }, // 权益折算系数
      { wch: 15 }, // 创新权益
      { wch: 12 }, // 归属年份
      { wch: 12 }, // 兑现日期
      { wch: 18 }  // 有效户折算户数
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '风险管理业务统计表')

    // 生成文件名
    const now = new Date()
    const fileName = `风险管理业务统计表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}
</script>

<style lang="scss" scoped>
/* temp  */
</style>
